import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import './Hub.css';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket,
  FaRobot,
  FaSignOutAlt
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const [currentQuote, setCurrentQuote] = useState(0);

  // Logout function
  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Show success message
    message.success('Logged out successfully!');

    // Navigate to home page
    navigate('/');
  };

  const inspiringQuotes = [
    "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
    "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
    "Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const navigationItems = [
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: FaBook,
      path: '/user/study-material',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: 'Ask AI',
      description: 'Get instant help from AI',
      icon: FaRobot,
      path: '/user/chat',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: 'Reports',
      description: 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-orange-500 to-orange-600',
      hoverColor: 'from-orange-600 to-orange-700'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Profile',
      description: 'Manage your account',
      icon: FaUser,
      path: '/user/profile',
      color: 'from-indigo-500 to-indigo-600',
      hoverColor: 'from-indigo-600 to-indigo-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/user/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    },
    {
      title: 'Plans',
      description: 'Upgrade your learning',
      icon: FaCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600',
      hoverColor: 'from-emerald-600 to-emerald-700'
    },
    {
      title: 'About Us',
      description: 'Learn about our mission',
      icon: FaInfoCircle,
      path: '/user/about-us',
      color: 'from-cyan-500 to-cyan-600',
      hoverColor: 'from-cyan-600 to-cyan-700'
    }
  ];

  return (
    <div className="hub-container">
      <div className="hub-content">


        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="hub-header"
        >


          {/* Amazing Animated Study Smarter */}
          <div className="hub-welcome relative">
            <motion.div
              className="relative inline-block"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              {/* Study - with book/learning animation */}
              <motion.span
                className="relative inline-block mr-3"
                initial={{ opacity: 0, rotateY: -90 }}
                animate={{
                  opacity: 1,
                  rotateY: 0,
                  textShadow: [
                    "0 0 10px rgba(59, 130, 246, 0.6)",
                    "0 0 25px rgba(59, 130, 246, 0.9)",
                    "0 0 10px rgba(59, 130, 246, 0.6)"
                  ]
                }}
                transition={{
                  duration: 1.2,
                  delay: 0.4,
                  textShadow: {
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }
                }}
                whileHover={{
                  scale: 1.1,
                  rotate: [0, -3, 3, 0],
                  transition: { duration: 0.4 }
                }}
                style={{
                  background: 'linear-gradient(45deg, #1e40af, #3b82f6, #60a5fa)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  fontWeight: '900',
                  fontSize: 'inherit',
                  textShadow: '0 0 15px rgba(59, 130, 246, 0.6)'
                }}
              >
                Study

                {/* Book icon animation */}
                <motion.div
                  className="absolute -top-2 -right-2 text-blue-500"
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{ fontSize: '0.8em' }}
                >
                  📚
                </motion.div>
              </motion.span>

              {/* Smarter - with brain/intelligence animation */}
              <motion.span
                className="relative inline-block"
                initial={{ opacity: 0, rotateY: 90 }}
                animate={{
                  opacity: 1,
                  rotateY: 0,
                  textShadow: [
                    "0 0 10px rgba(16, 185, 129, 0.6)",
                    "0 0 25px rgba(16, 185, 129, 0.9)",
                    "0 0 10px rgba(16, 185, 129, 0.6)"
                  ]
                }}
                transition={{
                  duration: 1.2,
                  delay: 0.7,
                  textShadow: {
                    duration: 2.8,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }
                }}
                whileHover={{
                  scale: 1.1,
                  rotate: [0, 3, -3, 0],
                  transition: { duration: 0.4 }
                }}
                style={{
                  background: 'linear-gradient(45deg, #059669, #10b981, #34d399)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  fontWeight: '900',
                  fontSize: 'inherit',
                  textShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                }}
              >
                Smarter

                {/* Brain icon animation */}
                <motion.div
                  className="absolute -top-2 -right-2 text-green-500"
                  animate={{
                    scale: [1, 1.3, 1],
                    rotate: [0, 15, -15, 0],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                  style={{ fontSize: '0.8em' }}
                >
                  🧠
                </motion.div>
              </motion.span>

              {/* User name with better visibility */}
              <motion.span
                className="ml-3"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{
                  opacity: 1,
                  scale: 1
                }}
                transition={{
                  duration: 1,
                  delay: 1
                }}
                whileHover={{
                  scale: 1.15,
                  rotate: [0, 5, -5, 0],
                  transition: { duration: 0.3 }
                }}
                style={{
                  fontWeight: '800',
                  color: '#1f2937',
                  textShadow: '0 0 10px rgba(31, 41, 55, 0.3)'
                }}
              >
                {user?.name}!

                {/* Celebration sparkles */}
                <motion.div
                  className="absolute -top-1 -right-1"
                  animate={{
                    rotate: [0, 360],
                    scale: [0.8, 1.2, 0.8],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{ fontSize: '0.6em' }}
                >
                  ✨
                </motion.div>
              </motion.span>
            </motion.div>

            {/* Magical underline effect */}
            <motion.div
              className="absolute -bottom-2 left-0 h-1 rounded-full"
              initial={{ width: 0, opacity: 0 }}
              animate={{
                width: '100%',
                opacity: 1,
                background: [
                  'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)',
                  'linear-gradient(90deg, #10b981, #f59e0b, #3b82f6)',
                  'linear-gradient(90deg, #f59e0b, #3b82f6, #10b981)',
                  'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)'
                ]
              }}
              transition={{
                duration: 1.5,
                delay: 1.5,
                background: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
              style={{
                background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)',
                boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'
              }}
            />
          </div>

          <p className="hub-subtitle">
            Ready to shine today? ✨ Choose your learning path below.
          </p>

          <div className="hub-quote">
            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />
            "{inspiringQuotes[currentQuote]}"
            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />
            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>
              - BrainWave Team
            </div>
          </div>
        </motion.div>

        <div className="hub-grid-container">
          <div className="hub-grid">
            {navigationItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`hub-card hover:${item.hoverColor} ${item.color}`}
                  onClick={() => navigate(item.path)}
                  tabIndex={0}
                  role="button"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      navigate(item.path);
                    }
                  }}
                >
                  <div className="hub-card-icon">
                    <IconComponent />
                  </div>

                  <h3 className="hub-card-title">
                    {item.title}
                  </h3>

                  <p className="hub-card-description">
                    {item.description}
                  </p>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="hub-bottom-decoration"
          >
            <div className="decoration-content">
              <FaGraduationCap className="decoration-icon animate-bounce-gentle" />
              <span>Your learning journey starts here!</span>
              <FaRocket className="decoration-icon animate-bounce-gentle" />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Hub;