import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import './Hub.css';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket,
  FaRobot,
  FaSignOutAlt
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const [currentQuote, setCurrentQuote] = useState(0);

  // Logout function
  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Show success message
    message.success('Logged out successfully!');

    // Navigate to home page
    navigate('/');
  };

  const inspiringQuotes = [
    "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
    "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
    "Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const navigationItems = [
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: FaBook,
      path: '/user/study-material',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: 'Ask AI',
      description: 'Get instant help from AI',
      icon: FaRobot,
      path: '/user/chat',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: 'Reports',
      description: 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-orange-500 to-orange-600',
      hoverColor: 'from-orange-600 to-orange-700'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Profile',
      description: 'Manage your account',
      icon: FaUser,
      path: '/user/profile',
      color: 'from-indigo-500 to-indigo-600',
      hoverColor: 'from-indigo-600 to-indigo-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/user/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    },
    {
      title: 'Plans',
      description: 'Upgrade your learning',
      icon: FaCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600',
      hoverColor: 'from-emerald-600 to-emerald-700'
    },
    {
      title: 'About Us',
      description: 'Learn about our mission',
      icon: FaInfoCircle,
      path: '/user/about-us',
      color: 'from-cyan-500 to-cyan-600',
      hoverColor: 'from-cyan-600 to-cyan-700'
    }
  ];

  return (
    <div className="hub-container">
      <div className="hub-content">


        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="hub-header"
        >


          {/* Professional Premium Study Smarter Animation */}
          <div className="hub-welcome relative overflow-hidden">
            <motion.div
              className="relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1.5 }}
            >
              {/* Premium Background Glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{
                  opacity: [0.1, 0.3, 0.1],
                  scale: [0.8, 1.1, 0.8]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                style={{
                  background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.15), rgba(16, 185, 129, 0.1), transparent)',
                  filter: 'blur(20px)'
                }}
              />

              {/* Main Text Container */}
              <motion.div
                className="relative z-10 flex items-center justify-center"
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 1.2, delay: 0.3, ease: "easeOut" }}
              >
                {/* Study - Premium Design */}
                <motion.div
                  className="relative mr-4"
                  initial={{ x: -100, opacity: 0, rotateX: -90 }}
                  animate={{
                    x: 0,
                    opacity: 1,
                    rotateX: 0
                  }}
                  transition={{
                    duration: 1.5,
                    delay: 0.6,
                    ease: "easeOut"
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateY: [0, 5, -5, 0],
                    transition: { duration: 0.6, ease: "easeInOut" }
                  }}
                >
                  <motion.span
                    className="block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight"
                    style={{
                      background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      fontFamily: "'Inter', 'SF Pro Display', system-ui, sans-serif",
                      letterSpacing: '-0.05em',
                      textShadow: '0 0 40px rgba(59, 130, 246, 0.3)'
                    }}
                    animate={{
                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    Study
                  </motion.span>

                  {/* Premium Floating Elements */}
                  <motion.div
                    className="absolute -top-3 -right-3"
                    animate={{
                      y: [-5, 5, -5],
                      rotate: [0, 10, -10, 0],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    style={{
                      fontSize: '1.5rem',
                      filter: 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.5))'
                    }}
                  >
                    📖
                  </motion.div>
                </motion.div>

                {/* Smarter - Premium Design */}
                <motion.div
                  className="relative"
                  initial={{ x: 100, opacity: 0, rotateX: 90 }}
                  animate={{
                    x: 0,
                    opacity: 1,
                    rotateX: 0
                  }}
                  transition={{
                    duration: 1.5,
                    delay: 0.9,
                    ease: "easeOut"
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateY: [0, -5, 5, 0],
                    transition: { duration: 0.6, ease: "easeInOut" }
                  }}
                >
                  <motion.span
                    className="block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight"
                    style={{
                      background: 'linear-gradient(135deg, #064e3b 0%, #059669 25%, #10b981 50%, #34d399 75%, #a7f3d0 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      fontFamily: "'Inter', 'SF Pro Display', system-ui, sans-serif",
                      letterSpacing: '-0.05em',
                      textShadow: '0 0 40px rgba(16, 185, 129, 0.3)'
                    }}
                    animate={{
                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    Smarter
                  </motion.span>

                  {/* Premium Floating Elements */}
                  <motion.div
                    className="absolute -top-3 -right-3"
                    animate={{
                      y: [5, -5, 5],
                      rotate: [0, -15, 15, 0],
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.5
                    }}
                    style={{
                      fontSize: '1.5rem',
                      filter: 'drop-shadow(0 0 10px rgba(16, 185, 129, 0.5))'
                    }}
                  >
                    🎯
                  </motion.div>
                </motion.div>
              </motion.div>

              {/* User Name - Premium Style */}
              <motion.div
                className="text-center mt-6"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 1.5 }}
              >
                <motion.span
                  className="text-2xl sm:text-3xl font-bold"
                  style={{
                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: '0 0 20px rgba(245, 158, 11, 0.3)'
                  }}
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  whileHover={{
                    scale: 1.1,
                    transition: { duration: 0.3 }
                  }}
                >
                  {user?.name}!
                </motion.span>

                {/* Premium Sparkle Effect */}
                <motion.div
                  className="absolute top-0 right-0"
                  animate={{
                    rotate: [0, 360],
                    scale: [0.8, 1.3, 0.8],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{
                    fontSize: '1.2rem',
                    filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.6))'
                  }}
                >
                  ⭐
                </motion.div>
              </motion.div>

              {/* Premium Underline Animation */}
              <motion.div
                className="absolute -bottom-4 left-1/2 transform -translate-x-1/2"
                initial={{ width: 0, opacity: 0 }}
                animate={{
                  width: '80%',
                  opacity: 1
                }}
                transition={{ duration: 2, delay: 2 }}
              >
                <motion.div
                  className="h-1 rounded-full relative overflow-hidden"
                  style={{
                    background: 'linear-gradient(90deg, transparent, #3b82f6, #10b981, #f59e0b, transparent)',
                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'
                  }}
                >
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: 'linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0))',
                      width: '30%'
                    }}
                    animate={{
                      x: ['-100%', '400%']
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 2.5
                    }}
                  />
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          <p className="hub-subtitle">
            Ready to shine today? ✨ Choose your learning path below.
          </p>

          <div className="hub-quote">
            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />
            "{inspiringQuotes[currentQuote]}"
            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />
            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>
              - BrainWave Team
            </div>
          </div>
        </motion.div>

        <div className="hub-grid-container">
          <div className="hub-grid">
            {navigationItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`hub-card hover:${item.hoverColor} ${item.color}`}
                  onClick={() => navigate(item.path)}
                  tabIndex={0}
                  role="button"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      navigate(item.path);
                    }
                  }}
                >
                  <div className="hub-card-icon">
                    <IconComponent />
                  </div>

                  <h3 className="hub-card-title">
                    {item.title}
                  </h3>

                  <p className="hub-card-description">
                    {item.description}
                  </p>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="hub-bottom-decoration"
          >
            <div className="decoration-content">
              <FaGraduationCap className="decoration-icon animate-bounce-gentle" />
              <span>Your learning journey starts here!</span>
              <FaRocket className="decoration-icon animate-bounce-gentle" />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Hub;