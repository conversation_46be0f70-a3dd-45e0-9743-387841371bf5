import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import './Hub.css';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket,
  FaRobot,
  FaSignOutAlt
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const [currentQuote, setCurrentQuote] = useState(0);

  // Logout function
  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Show success message
    message.success('Logged out successfully!');

    // Navigate to home page
    navigate('/');
  };

  const inspiringQuotes = [
    "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
    "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
    "Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const navigationItems = [
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: FaBook,
      path: '/user/study-material',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: 'Ask AI',
      description: 'Get instant help from AI',
      icon: FaRobot,
      path: '/user/chat',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: 'Reports',
      description: 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-orange-500 to-orange-600',
      hoverColor: 'from-orange-600 to-orange-700'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Profile',
      description: 'Manage your account',
      icon: FaUser,
      path: '/user/profile',
      color: 'from-indigo-500 to-indigo-600',
      hoverColor: 'from-indigo-600 to-indigo-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/user/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    },
    {
      title: 'Plans',
      description: 'Upgrade your learning',
      icon: FaCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600',
      hoverColor: 'from-emerald-600 to-emerald-700'
    },
    {
      title: 'About Us',
      description: 'Learn about our mission',
      icon: FaInfoCircle,
      path: '/user/about-us',
      color: 'from-cyan-500 to-cyan-600',
      hoverColor: 'from-cyan-600 to-cyan-700'
    }
  ];

  return (
    <div className="hub-container">
      <div className="hub-content">


        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="hub-header"
        >


          {/* Dynamic Inspiring Study Smarter Animation */}
          <div className="hub-welcome relative overflow-hidden min-h-[200px]">
            {/* Floating Study Icons Background */}
            <div className="absolute inset-0 pointer-events-none">
              {/* Floating Icons - Using Text Symbols */}
              <motion.div
                className="absolute top-10 left-10 text-3xl opacity-20 font-bold text-blue-600"
                animate={{
                  y: [-10, 10, -10],
                  rotate: [0, 15, -15, 0],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ★
              </motion.div>

              <motion.div
                className="absolute top-20 right-16 text-2xl opacity-25 font-bold text-green-600"
                animate={{
                  y: [15, -15, 15],
                  x: [-5, 5, -5],
                  rotate: [0, -20, 20, 0],
                  opacity: [0.25, 0.5, 0.25]
                }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ♦
              </motion.div>

              <motion.div
                className="absolute bottom-16 left-20 text-2xl opacity-20 font-bold text-purple-600"
                animate={{
                  y: [8, -8, 8],
                  rotate: [0, 25, -25, 0],
                  scale: [1, 1.2, 1],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{ duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 2 }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ●
              </motion.div>

              <motion.div
                className="absolute top-32 left-1/2 text-2xl opacity-15 font-bold text-orange-600"
                animate={{
                  y: [-12, 12, -12],
                  x: [-8, 8, -8],
                  rotate: [0, 30, -30, 0],
                  opacity: [0.15, 0.35, 0.15]
                }}
                transition={{ duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ▲
              </motion.div>

              <motion.div
                className="absolute bottom-20 right-12 text-3xl opacity-20 font-bold text-pink-600"
                animate={{
                  y: [10, -10, 10],
                  rotate: [0, -18, 18, 0],
                  scale: [1, 1.1, 1],
                  opacity: [0.2, 0.45, 0.2]
                }}
                transition={{ duration: 3.8, repeat: Infinity, ease: "easeInOut", delay: 1.5 }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ♠
              </motion.div>

              <motion.div
                className="absolute top-16 right-32 text-2xl opacity-25 font-bold text-yellow-600"
                animate={{
                  y: [-8, 8, -8],
                  x: [6, -6, 6],
                  rotate: [0, 22, -22, 0],
                  opacity: [0.25, 0.5, 0.25]
                }}
                transition={{ duration: 4.2, repeat: Infinity, ease: "easeInOut", delay: 2.5 }}
                style={{ fontFamily: 'Arial, sans-serif' }}
              >
                ✦
              </motion.div>
            </div>

            {/* Dynamic Background Waves */}
            <motion.div
              className="absolute inset-0 rounded-3xl"
              style={{
                background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',
                filter: 'blur(30px)'
              }}
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.3, 0.6, 0.3],
                rotate: [0, 2, -2, 0]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            {/* Main Content */}
            <motion.div
              className="relative z-10 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1.5 }}
            >
              {/* Study Text with Motion */}
              <motion.div
                className="relative inline-block mr-6"
                initial={{ x: -200, opacity: 0, rotateY: -90 }}
                animate={{
                  x: 0,
                  opacity: 1,
                  rotateY: 0
                }}
                transition={{
                  duration: 1.8,
                  delay: 0.5,
                  ease: "easeOut"
                }}
              >
                <motion.span
                  className="block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight"
                  style={{
                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    backgroundSize: '200% 200%',
                    fontFamily: "'Inter', 'SF Pro Display', system-ui, sans-serif",
                    letterSpacing: '-0.06em',
                    textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'
                  }}
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                    y: [0, -5, 0],
                    textShadow: [
                      '0 0 50px rgba(59, 130, 246, 0.4)',
                      '0 0 80px rgba(59, 130, 246, 0.7)',
                      '0 0 50px rgba(59, 130, 246, 0.4)'
                    ]
                  }}
                  transition={{
                    backgroundPosition: { duration: 6, repeat: Infinity, ease: "easeInOut" },
                    y: { duration: 3, repeat: Infinity, ease: "easeInOut" },
                    textShadow: { duration: 4, repeat: Infinity, ease: "easeInOut" }
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateZ: [0, 2, -2, 0],
                    transition: { duration: 0.6 }
                  }}
                >
                  Study
                </motion.span>

                {/* Dynamic Study Icons around "Study" */}
                <motion.div
                  className="absolute -top-8 -left-4 text-4xl font-bold text-blue-500"
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.3, 1],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 15px rgba(59, 130, 246, 0.6))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ◆
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -right-6 text-3xl font-bold text-blue-400"
                  animate={{
                    y: [-8, 8, -8],
                    rotate: [0, -25, 25, 0],
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 12px rgba(59, 130, 246, 0.5))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ▼
                </motion.div>
              </motion.div>

              {/* Smarter Text with Motion */}
              <motion.div
                className="relative inline-block"
                initial={{ x: 200, opacity: 0, rotateY: 90 }}
                animate={{
                  x: 0,
                  opacity: 1,
                  rotateY: 0
                }}
                transition={{
                  duration: 1.8,
                  delay: 1,
                  ease: "easeOut"
                }}
              >
                <motion.span
                  className="block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight"
                  style={{
                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    backgroundSize: '200% 200%',
                    fontFamily: "'Inter', 'SF Pro Display', system-ui, sans-serif",
                    letterSpacing: '-0.06em',
                    textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'
                  }}
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                    y: [0, 5, 0],
                    textShadow: [
                      '0 0 50px rgba(16, 185, 129, 0.4)',
                      '0 0 80px rgba(16, 185, 129, 0.7)',
                      '0 0 50px rgba(16, 185, 129, 0.4)'
                    ]
                  }}
                  transition={{
                    backgroundPosition: { duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 },
                    y: { duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 },
                    textShadow: { duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 1 }
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateZ: [0, -2, 2, 0],
                    transition: { duration: 0.6 }
                  }}
                >
                  Smarter
                </motion.span>

                {/* Dynamic Smart Icons around "Smarter" */}
                <motion.div
                  className="absolute -top-8 -right-4 text-4xl font-bold text-green-500"
                  animate={{
                    rotate: [0, -360],
                    scale: [1, 1.4, 1],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 7,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 15px rgba(16, 185, 129, 0.6))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ♣
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -left-6 text-3xl font-bold text-green-400"
                  animate={{
                    y: [8, -8, 8],
                    rotate: [0, 30, -30, 0],
                    scale: [1, 1.3, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 4.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 12px rgba(16, 185, 129, 0.5))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ◉
                </motion.div>
              </motion.div>

              {/* User Name with Inspiring Animation */}
              <motion.div
                className="mt-8 relative"
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 1.5, delay: 2 }}
              >
                <motion.span
                  className="text-3xl sm:text-4xl font-bold block"
                  style={{
                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    backgroundSize: '200% 200%',
                    textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'
                  }}
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                    scale: [1, 1.02, 1],
                    textShadow: [
                      '0 0 30px rgba(245, 158, 11, 0.4)',
                      '0 0 50px rgba(245, 158, 11, 0.7)',
                      '0 0 30px rgba(245, 158, 11, 0.4)'
                    ]
                  }}
                  transition={{
                    backgroundPosition: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                    scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                    textShadow: { duration: 3, repeat: Infinity, ease: "easeInOut" }
                  }}
                  whileHover={{
                    scale: 1.1,
                    rotate: [0, 3, -3, 0],
                    transition: { duration: 0.4 }
                  }}
                >
                  {user?.name}!
                </motion.span>

                {/* Celebration Icons */}
                <motion.div
                  className="absolute -top-4 -right-8 text-2xl font-bold text-yellow-500"
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.5, 1],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ★
                </motion.div>

                <motion.div
                  className="absolute -bottom-2 -left-8 text-xl font-bold text-orange-500"
                  animate={{
                    y: [-5, 5, -5],
                    rotate: [0, -20, 20, 0],
                    scale: [1, 1.3, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.5))',
                    fontFamily: 'Arial, sans-serif'
                  }}
                >
                  ✧
                </motion.div>
              </motion.div>

              {/* Dynamic Inspiring Underline */}
              <motion.div
                className="mt-6 relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1, delay: 2.5 }}
              >
                <motion.div
                  className="h-2 mx-auto rounded-full relative overflow-hidden"
                  style={{
                    width: '90%',
                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',
                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'
                  }}
                  animate={{
                    boxShadow: [
                      '0 0 30px rgba(59, 130, 246, 0.5)',
                      '0 0 50px rgba(16, 185, 129, 0.7)',
                      '0 0 40px rgba(245, 158, 11, 0.6)',
                      '0 0 30px rgba(59, 130, 246, 0.5)'
                    ]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  {/* Moving Light Effect */}
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',
                      width: '40%'
                    }}
                    animate={{
                      x: ['-100%', '250%']
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 3
                    }}
                  />
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          <p className="hub-subtitle">
            Ready to shine today? ✨ Choose your learning path below.
          </p>

          <div className="hub-quote">
            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />
            "{inspiringQuotes[currentQuote]}"
            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />
            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>
              - BrainWave Team
            </div>
          </div>
        </motion.div>

        <div className="hub-grid-container">
          <div className="hub-grid">
            {navigationItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`hub-card hover:${item.hoverColor} ${item.color}`}
                  onClick={() => navigate(item.path)}
                  tabIndex={0}
                  role="button"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      navigate(item.path);
                    }
                  }}
                >
                  <div className="hub-card-icon">
                    <IconComponent />
                  </div>

                  <h3 className="hub-card-title">
                    {item.title}
                  </h3>

                  <p className="hub-card-description">
                    {item.description}
                  </p>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="hub-bottom-decoration"
          >
            <div className="decoration-content">
              <FaGraduationCap className="decoration-icon animate-bounce-gentle" />
              <span>Your learning journey starts here!</span>
              <FaRocket className="decoration-icon animate-bounce-gentle" />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Hub;